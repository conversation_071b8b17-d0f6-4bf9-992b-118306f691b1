[2025-04-10T17:19:20.914Z] [31m[ERROR] Error translating files: [Erai-raws] yamihealer - 02 [1080p CR WEBRip HEVC EAC3][MultiSub][7290A6C8]_eng.txt  AxiosError: Request failed with status code 404
[2025-04-10T17:19:21.036Z] [31m[ERROR] Error in translating process: Command failed: node app/2translate/translate.js
[2025-04-13T17:19:55.943Z] [31m[ERROR] Failed to process file: app\0rss\downloads\[Erai-raws] Kakushite Makina-san - 01 [1080p CR WEB-DL AVC AAC][MultiSub][D4505F66].mkv Error: [31m[ERROR] FFmpeg error: 
Process exited with code 4294967274
[2025-04-13T17:20:17.653Z] [31m[ERROR] Failed to process file: app\0rss\downloads\[Erai-raws] Kakushite Makina-san - 01 [1080p CR WEB-DL AVC AAC][MultiSub][D4505F66].mkv Error: [31m[ERROR] FFmpeg error: 
Process exited with code 4294967274
[2025-04-13T17:38:55.285Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] aiomodarkelf - 02 [1080p ADN WEB-DL AVC AAC][MultiSub][94D7760F]\segment_0.03.42.70-**********.wav: ENOENT: no such file or directory, unlink 'D:\Projekty\splendour.cafe-bot - v2\app\audio_processing\[Erai-raws] aiomodarkelf - 02 [1080p ADN WEB-DL AVC AAC][MultiSub][94D7760F]\segment_0.03.42.70-**********.wav'
[2025-04-14T19:06:06.681Z] [31m[ERROR] Error in replacing process: Command failed: node app/3replace/1separateActors.js
[2025-04-14T19:06:06.708Z] [31m[ERROR] Error in translating process: Command failed: node app/2translate/translate.js
[2025-04-14T19:26:10.006Z] [31m[ERROR] Error in replacing process: Command failed: node app/3replace/1separateActors.js
[2025-04-14T19:26:10.029Z] [31m[ERROR] Error in translating process: Command failed: node app/2translate/translate.js
[2025-04-16T14:02:32.388Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] Lycoris Recoil - Friends are thieves of time. - 01 [1080p] [4C2D1A94]\segment_0.00.47.56-0.00.49.15.wav: ENOENT: no such file or directory, unlink 'D:\Projekty\splendour.cafe-bot - v2\app\audio_processing\[Erai-raws] Lycoris Recoil - Friends are thieves of time. - 01 [1080p] [4C2D1A94]\segment_0.00.47.56-0.00.49.15.wav'
[2025-04-16T14:02:32.392Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] Lycoris Recoil - Friends are thieves of time. - 01 [1080p] [4C2D1A94]\segment_0.02.00.22-0.02.02.72.wav: ENOENT: no such file or directory, unlink 'D:\Projekty\splendour.cafe-bot - v2\app\audio_processing\[Erai-raws] Lycoris Recoil - Friends are thieves of time. - 01 [1080p] [4C2D1A94]\segment_0.02.00.22-0.02.02.72.wav'
[2025-04-17T17:19:14.994Z] [31m[ERROR] Error translating files: [Erai-raws] yamihealer - 03 [1080p CR WEB-DL AVC AAC][MultiSub][1DE7EF06]_eng.txt  AxiosError: Request failed with status code 404
[2025-04-17T17:19:15.192Z] [31m[ERROR] Error in translating process: Command failed: node app/2translate/translate.js
[2025-04-21T22:01:26.881Z] [2025-04-21T22:01:26.881Z]
ReferenceError: titleMatch is not defined
    at processFeed (file:///D:/Projekty/splendour.cafe-bot%20-%20v2/app/0rss/download.js:453:33)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)

[2025-04-21T22:21:39.952Z] [31m[ERROR] Title match failed for: The.Mononoke.Lecture.Logs.of.Chuzenji-sensei.S01E03.The.Man.in.the.Blue.Cloak.1080p.B-Global.WEB-DL.JPN.AAC2.0.H.264.MSubs-ToonsHub.mkv
[2025-04-21T22:26:49.182Z] [31m[ERROR] Failed to create segment for 0:15:37.00-0:15:37.00: Failed to split audio segment: Command failed: ffmpeg -i "app\audio_processing\The.Mononoke.Lecture.Logs.of.Chuzenji-sensei.S01E03.The.Man.in.the.Blue.Cloak.1080p.B-Global.WEB-DL.JPN.AAC2.0.H.264.MSubs-ToonsHub\full.wav" -ss 0:15:37.00 -to 0:15:37.00 -acodec copy "app\audio_processing\The.Mononoke.Lecture.Logs.of.Chuzenji-sensei.S01E03.The.Man.in.the.Blue.Cloak.1080p.B-Global.WEB-DL.JPN.AAC2.0.H.264.MSubs-ToonsHub\segment_0.15.37.00-0.15.37.00.wav" -loglevel warning -stats -y
[aist#0:0/pcm_s16le @ 0000019e1d79fd40] Guessed Channel Layout: stereo
[out#0 @ 0000019e1d11a1c0] -to value smaller than -ss; aborting.
Error opening output file app\audio_processing\The.Mononoke.Lecture.Logs.of.Chuzenji-sensei.S01E03.The.Man.in.the.Blue.Cloak.1080p.B-Global.WEB-DL.JPN.AAC2.0.H.264.MSubs-ToonsHub\segment_0.15.37.00-0.15.37.00.wav.
Error opening output files: Invalid argument

[2025-04-21T22:26:49.355Z] [31m[ERROR] Failed to create segment for 0:19:43.10-0:19:43.10: Failed to split audio segment: Command failed: ffmpeg -i "app\audio_processing\The.Mononoke.Lecture.Logs.of.Chuzenji-sensei.S01E03.The.Man.in.the.Blue.Cloak.1080p.B-Global.WEB-DL.JPN.AAC2.0.H.264.MSubs-ToonsHub\full.wav" -ss 0:19:43.10 -to 0:19:43.10 -acodec copy "app\audio_processing\The.Mononoke.Lecture.Logs.of.Chuzenji-sensei.S01E03.The.Man.in.the.Blue.Cloak.1080p.B-Global.WEB-DL.JPN.AAC2.0.H.264.MSubs-ToonsHub\segment_0.19.43.10-0.19.43.10.wav" -loglevel warning -stats -y
[aist#0:0/pcm_s16le @ 000002dd6dc6fd40] Guessed Channel Layout: stereo
[out#0 @ 000002dd6d68a1c0] -to value smaller than -ss; aborting.
Error opening output file app\audio_processing\The.Mononoke.Lecture.Logs.of.Chuzenji-sensei.S01E03.The.Man.in.the.Blue.Cloak.1080p.B-Global.WEB-DL.JPN.AAC2.0.H.264.MSubs-ToonsHub\segment_0.19.43.10-0.19.43.10.wav.
Error opening output files: Invalid argument

[2025-04-21T22:48:08.438Z] [31m[ERROR] Error in replacing process: Command failed: node app/3replace/1separateActors.js
[2025-04-21T22:48:08.472Z] [31m[ERROR] Error in translating process: Command failed: node app/2translate/translate.js
[2025-04-21T22:48:08.496Z] [ERROR] Error in clearing process: Command failed: node app/1clear/clear.js
[2025-04-21T22:48:08.985Z] [2025-04-21T22:48:08.985Z] Error: Command failed: node app/1clear/clear.js
    at genericNodeError (node:internal/errors:984:15)
    at wrappedFn (node:internal/errors:538:14)
    at checkExecSyncError (node:child_process:891:11)
    at Module.execSync (node:child_process:963:15)
    at startClearingProcess (file:///D:/Projekty/splendour.cafe-bot%20-%20v2/app/0rss/download.js:1087:8)
    at processNextDownload (file:///D:/Projekty/splendour.cafe-bot%20-%20v2/app/0rss/download.js:250:5)
    at Timeout.<anonymous> (file:///D:/Projekty/splendour.cafe-bot%20-%20v2/app/0rss/download.js:314:11)
    at listOnTimeout (node:internal/timers:581:17)
    at process.processTimers (node:internal/timers:519:7)

[2025-04-23T17:47:34.805Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] Lycoris Recoil - Friends Are Thieves of Time - 02 [1080p CR WEB-DL AVC AAC][MultiSub][893B07C2]\segment_0.01.49.88-0.01.51.13.wav: ENOENT: no such file or directory, unlink 'D:\Projekty\splendour.cafe-bot - v2\app\audio_processing\[Erai-raws] Lycoris Recoil - Friends Are Thieves of Time - 02 [1080p CR WEB-DL AVC AAC][MultiSub][893B07C2]\segment_0.01.49.88-0.01.51.13.wav'
[2025-04-26T09:59:48.460Z] [ERROR] Attempt 1: Failed to fetch RSS feed: Request failed with status code 403
[2025-04-26T09:59:58.581Z] [ERROR] Attempt 2: Failed to fetch RSS feed: Request failed with status code 403
[2025-04-27T20:56:46.973Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] aiomodarkelf - 04 [1080p CR WEB-DL AVC AAC][MultiSub][8B56226C]\segment_0.00.31.63-0.00.33.59.wav: ENOENT: no such file or directory, unlink 'D:\Projekty\splendour.cafe-bot - v2\app\audio_processing\[Erai-raws] aiomodarkelf - 04 [1080p CR WEB-DL AVC AAC][MultiSub][8B56226C]\segment_0.00.31.63-0.00.33.59.wav'
[2025-04-27T21:56:36.864Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] aiomodarkelf - 04 [1080p CR WEB-DL AVC AAC][MultiSub][8B56226C]\segment_0.00.26.71-0.00.28.47.wav: ENOENT: no such file or directory, unlink 'D:\Projekty\splendour.cafe-bot - v2\app\audio_processing\[Erai-raws] aiomodarkelf - 04 [1080p CR WEB-DL AVC AAC][MultiSub][8B56226C]\segment_0.00.26.71-0.00.28.47.wav'
[2025-05-01T09:42:23.948Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] Lycoris Recoil - Friends Are Thieves of Time - 03 [1080p CR WEB-DL AVC AAC][MultiSub][4C7DC11A]\segment_0.01.53.34-0.01.54.21.wav: ENOENT: no such file or directory, unlink 'D:\Projekty\splendour.cafe-bot - v2\app\audio_processing\[Erai-raws] Lycoris Recoil - Friends Are Thieves of Time - 03 [1080p CR WEB-DL AVC AAC][MultiSub][4C7DC11A]\segment_0.01.53.34-0.01.54.21.wav'
[2025-05-01T09:42:23.949Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] Lycoris Recoil - Friends Are Thieves of Time - 03 [1080p CR WEB-DL AVC AAC][MultiSub][4C7DC11A]\segment_0.01.54.21-0.01.58.22.wav: ENOENT: no such file or directory, unlink 'D:\Projekty\splendour.cafe-bot - v2\app\audio_processing\[Erai-raws] Lycoris Recoil - Friends Are Thieves of Time - 03 [1080p CR WEB-DL AVC AAC][MultiSub][4C7DC11A]\segment_0.01.54.21-0.01.58.22.wav'
[2025-05-04T20:44:55.530Z] [31m[ERROR] Error translating files: Kakushite.Makina-san.S01E05.REPACK.1080p.UNCENSORED.ADN.WEB-DL.JPN.AAC2.0.H.264.MSubs-ToonsHub_eng.txt  AxiosError: Request failed with status code 404
[2025-05-04T20:44:55.666Z] [31m[ERROR] Error in translating process: Command failed: node app/2translate/translate.js
[2025-05-06T17:57:53.508Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] Rock wa Lady no Tashinami deshite - 05 [1080p AMZN WEB-DL AVC EAC3][MultiSub][6C045ED3]\segment_0.04.47.71-0.04.49.29.wav: ENOENT: no such file or directory, unlink 'D:\Projekty\splendour.cafe-bot - v2\app\audio_processing\[Erai-raws] Rock wa Lady no Tashinami deshite - 05 [1080p AMZN WEB-DL AVC EAC3][MultiSub][6C045ED3]\segment_0.04.47.71-0.04.49.29.wav'
[2025-05-06T17:57:53.510Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] Rock wa Lady no Tashinami deshite - 05 [1080p AMZN WEB-DL AVC EAC3][MultiSub][6C045ED3]\segment_0.04.46.00-0.04.47.71.wav: ENOENT: no such file or directory, unlink 'D:\Projekty\splendour.cafe-bot - v2\app\audio_processing\[Erai-raws] Rock wa Lady no Tashinami deshite - 05 [1080p AMZN WEB-DL AVC EAC3][MultiSub][6C045ED3]\segment_0.04.46.00-0.04.47.71.wav'
[2025-05-06T17:57:53.511Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] Rock wa Lady no Tashinami deshite - 05 [1080p AMZN WEB-DL AVC EAC3][MultiSub][6C045ED3]\segment_0.04.46.00-0.04.47.71.wav: ENOENT: no such file or directory, unlink 'D:\Projekty\splendour.cafe-bot - v2\app\audio_processing\[Erai-raws] Rock wa Lady no Tashinami deshite - 05 [1080p AMZN WEB-DL AVC EAC3][MultiSub][6C045ED3]\segment_0.04.46.00-0.04.47.71.wav'
[2025-05-06T17:57:53.515Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] Rock wa Lady no Tashinami deshite - 05 [1080p AMZN WEB-DL AVC EAC3][MultiSub][6C045ED3]\segment_0.04.46.00-0.04.47.71.wav: ENOENT: no such file or directory, unlink 'D:\Projekty\splendour.cafe-bot - v2\app\audio_processing\[Erai-raws] Rock wa Lady no Tashinami deshite - 05 [1080p AMZN WEB-DL AVC EAC3][MultiSub][6C045ED3]\segment_0.04.46.00-0.04.47.71.wav'
[2025-05-06T17:57:53.517Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] Rock wa Lady no Tashinami deshite - 05 [1080p AMZN WEB-DL AVC EAC3][MultiSub][6C045ED3]\segment_0.09.49.13-0.09.50.38.wav: ENOENT: no such file or directory, unlink 'D:\Projekty\splendour.cafe-bot - v2\app\audio_processing\[Erai-raws] Rock wa Lady no Tashinami deshite - 05 [1080p AMZN WEB-DL AVC EAC3][MultiSub][6C045ED3]\segment_0.09.49.13-0.09.50.38.wav'
[2025-05-06T17:57:53.518Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] Rock wa Lady no Tashinami deshite - 05 [1080p AMZN WEB-DL AVC EAC3][MultiSub][6C045ED3]\segment_0.09.49.13-0.09.50.38.wav: ENOENT: no such file or directory, unlink 'D:\Projekty\splendour.cafe-bot - v2\app\audio_processing\[Erai-raws] Rock wa Lady no Tashinami deshite - 05 [1080p AMZN WEB-DL AVC EAC3][MultiSub][6C045ED3]\segment_0.09.49.13-0.09.50.38.wav'
[2025-05-06T17:57:53.527Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] Rock wa Lady no Tashinami deshite - 05 [1080p AMZN WEB-DL AVC EAC3][MultiSub][6C045ED3]\segment_0.16.52.29-0.16.53.08.wav: ENOENT: no such file or directory, unlink 'D:\Projekty\splendour.cafe-bot - v2\app\audio_processing\[Erai-raws] Rock wa Lady no Tashinami deshite - 05 [1080p AMZN WEB-DL AVC EAC3][MultiSub][6C045ED3]\segment_0.16.52.29-0.16.53.08.wav'
[2025-05-08T16:53:22.918Z] [2025-05-08T16:53:22.918Z] Error: Failed to extract title
    at Timeout._onTimeout (file:///D:/Projekty/splendour.cafe-bot%20-%20v2/app/0rss/download.js:299:21)
    at listOnTimeout (node:internal/timers:581:17)
    at process.processTimers (node:internal/timers:519:7)

[2025-05-08T16:58:50.917Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] aiomodarkelf - 05 [1080p ADN WEB-DL AVC AAC][MultiSub][2FFCFE43]\segment_0.02.32.05-0.02.36.47.wav: ENOENT: no such file or directory, unlink 'D:\Projekty\splendour.cafe-bot - v2\app\audio_processing\[Erai-raws] aiomodarkelf - 05 [1080p ADN WEB-DL AVC AAC][MultiSub][2FFCFE43]\segment_0.02.32.05-0.02.36.47.wav'
[2025-05-08T16:58:50.918Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] aiomodarkelf - 05 [1080p ADN WEB-DL AVC AAC][MultiSub][2FFCFE43]\segment_0.02.32.05-0.02.36.47.wav: ENOENT: no such file or directory, unlink 'D:\Projekty\splendour.cafe-bot - v2\app\audio_processing\[Erai-raws] aiomodarkelf - 05 [1080p ADN WEB-DL AVC AAC][MultiSub][2FFCFE43]\segment_0.02.32.05-0.02.36.47.wav'
[2025-05-08T16:58:50.921Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] aiomodarkelf - 05 [1080p ADN WEB-DL AVC AAC][MultiSub][2FFCFE43]\segment_0.03.58.43-0.04.01.14.wav: ENOENT: no such file or directory, unlink 'D:\Projekty\splendour.cafe-bot - v2\app\audio_processing\[Erai-raws] aiomodarkelf - 05 [1080p ADN WEB-DL AVC AAC][MultiSub][2FFCFE43]\segment_0.03.58.43-0.04.01.14.wav'
[2025-05-08T16:58:50.923Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] aiomodarkelf - 05 [1080p ADN WEB-DL AVC AAC][MultiSub][2FFCFE43]\segment_0.06.16.02-0.06.19.53.wav: ENOENT: no such file or directory, unlink 'D:\Projekty\splendour.cafe-bot - v2\app\audio_processing\[Erai-raws] aiomodarkelf - 05 [1080p ADN WEB-DL AVC AAC][MultiSub][2FFCFE43]\segment_0.06.16.02-0.06.19.53.wav'
[2025-05-08T16:58:50.923Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] aiomodarkelf - 05 [1080p ADN WEB-DL AVC AAC][MultiSub][2FFCFE43]\segment_0.06.16.02-0.06.19.53.wav: ENOENT: no such file or directory, unlink 'D:\Projekty\splendour.cafe-bot - v2\app\audio_processing\[Erai-raws] aiomodarkelf - 05 [1080p ADN WEB-DL AVC AAC][MultiSub][2FFCFE43]\segment_0.06.16.02-0.06.19.53.wav'
[2025-05-08T16:58:50.924Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] aiomodarkelf - 05 [1080p ADN WEB-DL AVC AAC][MultiSub][2FFCFE43]\segment_0.04.14.61-0.04.15.74.wav: ENOENT: no such file or directory, unlink 'D:\Projekty\splendour.cafe-bot - v2\app\audio_processing\[Erai-raws] aiomodarkelf - 05 [1080p ADN WEB-DL AVC AAC][MultiSub][2FFCFE43]\segment_0.04.14.61-0.04.15.74.wav'
[2025-05-08T16:58:50.924Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] aiomodarkelf - 05 [1080p ADN WEB-DL AVC AAC][MultiSub][2FFCFE43]\segment_0.04.13.52-0.04.14.61.wav: ENOENT: no such file or directory, unlink 'D:\Projekty\splendour.cafe-bot - v2\app\audio_processing\[Erai-raws] aiomodarkelf - 05 [1080p ADN WEB-DL AVC AAC][MultiSub][2FFCFE43]\segment_0.04.13.52-0.04.14.61.wav'
[2025-05-08T16:58:50.924Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] aiomodarkelf - 05 [1080p ADN WEB-DL AVC AAC][MultiSub][2FFCFE43]\segment_0.04.13.52-0.04.14.61.wav: ENOENT: no such file or directory, unlink 'D:\Projekty\splendour.cafe-bot - v2\app\audio_processing\[Erai-raws] aiomodarkelf - 05 [1080p ADN WEB-DL AVC AAC][MultiSub][2FFCFE43]\segment_0.04.13.52-0.04.14.61.wav'
[2025-05-08T16:58:50.925Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] aiomodarkelf - 05 [1080p ADN WEB-DL AVC AAC][MultiSub][2FFCFE43]\segment_0.04.13.52-0.04.14.61.wav: ENOENT: no such file or directory, unlink 'D:\Projekty\splendour.cafe-bot - v2\app\audio_processing\[Erai-raws] aiomodarkelf - 05 [1080p ADN WEB-DL AVC AAC][MultiSub][2FFCFE43]\segment_0.04.13.52-0.04.14.61.wav'
[2025-05-08T16:58:50.925Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] aiomodarkelf - 05 [1080p ADN WEB-DL AVC AAC][MultiSub][2FFCFE43]\segment_0.04.12.40-0.04.13.52.wav: ENOENT: no such file or directory, unlink 'D:\Projekty\splendour.cafe-bot - v2\app\audio_processing\[Erai-raws] aiomodarkelf - 05 [1080p ADN WEB-DL AVC AAC][MultiSub][2FFCFE43]\segment_0.04.12.40-0.04.13.52.wav'
[2025-05-08T16:58:50.926Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] aiomodarkelf - 05 [1080p ADN WEB-DL AVC AAC][MultiSub][2FFCFE43]\segment_0.04.12.40-0.04.13.52.wav: ENOENT: no such file or directory, unlink 'D:\Projekty\splendour.cafe-bot - v2\app\audio_processing\[Erai-raws] aiomodarkelf - 05 [1080p ADN WEB-DL AVC AAC][MultiSub][2FFCFE43]\segment_0.04.12.40-0.04.13.52.wav'
[2025-05-08T16:58:50.926Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] aiomodarkelf - 05 [1080p ADN WEB-DL AVC AAC][MultiSub][2FFCFE43]\segment_0.04.14.61-0.04.15.74.wav: ENOENT: no such file or directory, unlink 'D:\Projekty\splendour.cafe-bot - v2\app\audio_processing\[Erai-raws] aiomodarkelf - 05 [1080p ADN WEB-DL AVC AAC][MultiSub][2FFCFE43]\segment_0.04.14.61-0.04.15.74.wav'
[2025-05-08T16:58:50.927Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] aiomodarkelf - 05 [1080p ADN WEB-DL AVC AAC][MultiSub][2FFCFE43]\segment_0.04.12.40-0.04.13.52.wav: ENOENT: no such file or directory, unlink 'D:\Projekty\splendour.cafe-bot - v2\app\audio_processing\[Erai-raws] aiomodarkelf - 05 [1080p ADN WEB-DL AVC AAC][MultiSub][2FFCFE43]\segment_0.04.12.40-0.04.13.52.wav'
[2025-05-08T16:58:50.927Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] aiomodarkelf - 05 [1080p ADN WEB-DL AVC AAC][MultiSub][2FFCFE43]\segment_0.04.12.40-0.04.13.52.wav: ENOENT: no such file or directory, unlink 'D:\Projekty\splendour.cafe-bot - v2\app\audio_processing\[Erai-raws] aiomodarkelf - 05 [1080p ADN WEB-DL AVC AAC][MultiSub][2FFCFE43]\segment_0.04.12.40-0.04.13.52.wav'
[2025-05-08T16:58:50.928Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] aiomodarkelf - 05 [1080p ADN WEB-DL AVC AAC][MultiSub][2FFCFE43]\segment_0.04.12.40-0.04.13.52.wav: ENOENT: no such file or directory, unlink 'D:\Projekty\splendour.cafe-bot - v2\app\audio_processing\[Erai-raws] aiomodarkelf - 05 [1080p ADN WEB-DL AVC AAC][MultiSub][2FFCFE43]\segment_0.04.12.40-0.04.13.52.wav'
[2025-05-08T16:58:50.929Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] aiomodarkelf - 05 [1080p ADN WEB-DL AVC AAC][MultiSub][2FFCFE43]\segment_0.06.16.02-0.06.19.53.wav: ENOENT: no such file or directory, unlink 'D:\Projekty\splendour.cafe-bot - v2\app\audio_processing\[Erai-raws] aiomodarkelf - 05 [1080p ADN WEB-DL AVC AAC][MultiSub][2FFCFE43]\segment_0.06.16.02-0.06.19.53.wav'
[2025-05-08T16:58:50.929Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] aiomodarkelf - 05 [1080p ADN WEB-DL AVC AAC][MultiSub][2FFCFE43]\segment_0.06.16.02-0.06.19.53.wav: ENOENT: no such file or directory, unlink 'D:\Projekty\splendour.cafe-bot - v2\app\audio_processing\[Erai-raws] aiomodarkelf - 05 [1080p ADN WEB-DL AVC AAC][MultiSub][2FFCFE43]\segment_0.06.16.02-0.06.19.53.wav'
[2025-05-08T17:44:16.693Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] Rock wa Lady no Tashinami deshite - 06 [1080p AMZN WEB-DL AVC EAC3][MultiSub][1DC8858B]\segment_0.04.06.71-0.04.09.71.wav: ENOENT: no such file or directory, unlink 'D:\Projekty\splendour.cafe-bot - v2\app\audio_processing\[Erai-raws] Rock wa Lady no Tashinami deshite - 06 [1080p AMZN WEB-DL AVC EAC3][MultiSub][1DC8858B]\segment_0.04.06.71-0.04.09.71.wav'
[2025-05-08T17:44:16.698Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] Rock wa Lady no Tashinami deshite - 06 [1080p AMZN WEB-DL AVC EAC3][MultiSub][1DC8858B]\segment_0.10.29.17-0.10.31.54.wav: EPERM: operation not permitted, unlink 'D:\Projekty\splendour.cafe-bot - v2\app\audio_processing\[Erai-raws] Rock wa Lady no Tashinami deshite - 06 [1080p AMZN WEB-DL AVC EAC3][MultiSub][1DC8858B]\segment_0.10.29.17-0.10.31.54.wav'
[2025-05-08T17:44:16.699Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] Rock wa Lady no Tashinami deshite - 06 [1080p AMZN WEB-DL AVC EAC3][MultiSub][1DC8858B]\segment_0.09.48.83-0.09.51.33.wav: ENOENT: no such file or directory, unlink 'D:\Projekty\splendour.cafe-bot - v2\app\audio_processing\[Erai-raws] Rock wa Lady no Tashinami deshite - 06 [1080p AMZN WEB-DL AVC EAC3][MultiSub][1DC8858B]\segment_0.09.48.83-0.09.51.33.wav'
[2025-05-08T17:44:16.700Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] Rock wa Lady no Tashinami deshite - 06 [1080p AMZN WEB-DL AVC EAC3][MultiSub][1DC8858B]\segment_0.09.44.33-0.09.46.83.wav: ENOENT: no such file or directory, unlink 'D:\Projekty\splendour.cafe-bot - v2\app\audio_processing\[Erai-raws] Rock wa Lady no Tashinami deshite - 06 [1080p AMZN WEB-DL AVC EAC3][MultiSub][1DC8858B]\segment_0.09.44.33-0.09.46.83.wav'
[2025-05-08T18:01:12.031Z] [31m[ERROR] Error translating files: [Erai-raws] yamihealer - 06 [1080p CR WEB-DL AVC AAC][MultiSub][CD88EFC5]_eng.txt  AxiosError: Request failed with status code 404
[2025-05-08T18:01:12.173Z] [31m[ERROR] Error in translating process: Command failed: node app/2translate/translate.js
[2025-05-11T19:16:00.113Z] [31m[ERROR] Error translating files: Kakushite.Makina-san.S01E06.REPACK.1080p.UNCENSORED.ADN.WEB-DL.JPN.AAC2.0.H.264.MSubs-ToonsHub_eng.txt  AxiosError: Request failed with status code 404
[2025-05-11T19:16:00.342Z] [31m[ERROR] Error in translating process: Command failed: node app/2translate/translate.js
[2025-05-11T21:37:49.575Z] [31m[ERROR] Error translating files: Kakushite.Makina-san.S01E06.REPACK.1080p.UNCENSORED.ADN.WEB-DL.JPN.AAC2.0.H.264.MSubs-ToonsHub_eng.txt  Error: [31m[ERROR] Line mismatch encountered during translations. Ending the job after 5 attempts.
[2025-05-11T21:37:49.629Z] [31m[ERROR] Error in translating process: Command failed: node app/2translate/translate.js
[2025-05-14T22:42:04.724Z] [31m[ERROR] Failed to create segment for 0:09:45.92-0:09:45.92: Failed to split audio segment: Command failed: ffmpeg -i "app\audio_processing\[Erai-raws] Uma Musume - Cinderella Gray - 06 [1080p AMZN WEB-DL AVC EAC3][MultiSub][E225F712]\full.wav" -ss 0:09:45.92 -to 0:09:45.92 -acodec copy "app\audio_processing\[Erai-raws] Uma Musume - Cinderella Gray - 06 [1080p AMZN WEB-DL AVC EAC3][MultiSub][E225F712]\segment_0.09.45.92-0.09.45.92.wav" -loglevel warning -stats -y
[aist#0:0/pcm_s16le @ 000002199ad70c40] Guessed Channel Layout: stereo
[out#0 @ 000002199ad70e00] -to value smaller than -ss; aborting.
Error opening output file app\audio_processing\[Erai-raws] Uma Musume - Cinderella Gray - 06 [1080p AMZN WEB-DL AVC EAC3][MultiSub][E225F712]\segment_0.09.45.92-0.09.45.92.wav.
Error opening output files: Invalid argument

[2025-05-19T14:48:09.417Z] [31m[ERROR] Failed to delete segment app\audio_processing\Kakushite.Makina-san.S01E07.1080p.UNCENSORED.ADN.WEB-DL.JPN.AAC2.0.H.264.MSubs-ToonsHub\segment_0.01.52.11-0.01.56.32.wav: ENOENT: no such file or directory, unlink 'D:\Projekty\splendour.cafe-bot - v2\app\audio_processing\Kakushite.Makina-san.S01E07.1080p.UNCENSORED.ADN.WEB-DL.JPN.AAC2.0.H.264.MSubs-ToonsHub\segment_0.01.52.11-0.01.56.32.wav'
[2025-05-19T14:53:05.029Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] aiomodarkelf - 07 [1080p ADN WEB-DL AVC AAC][MultiSub][447984CA]\segment_0.00.28.75-0.00.31.13.wav: ENOENT: no such file or directory, unlink 'D:\Projekty\splendour.cafe-bot - v2\app\audio_processing\[Erai-raws] aiomodarkelf - 07 [1080p ADN WEB-DL AVC AAC][MultiSub][447984CA]\segment_0.00.28.75-0.00.31.13.wav'
[2025-05-19T14:53:05.030Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] aiomodarkelf - 07 [1080p ADN WEB-DL AVC AAC][MultiSub][447984CA]\segment_0.00.28.75-0.00.31.13.wav: ENOENT: no such file or directory, unlink 'D:\Projekty\splendour.cafe-bot - v2\app\audio_processing\[Erai-raws] aiomodarkelf - 07 [1080p ADN WEB-DL AVC AAC][MultiSub][447984CA]\segment_0.00.28.75-0.00.31.13.wav'
[2025-05-19T14:53:05.031Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] aiomodarkelf - 07 [1080p ADN WEB-DL AVC AAC][MultiSub][447984CA]\segment_0.02.50.13-0.02.58.21.wav: ENOENT: no such file or directory, unlink 'D:\Projekty\splendour.cafe-bot - v2\app\audio_processing\[Erai-raws] aiomodarkelf - 07 [1080p ADN WEB-DL AVC AAC][MultiSub][447984CA]\segment_0.02.50.13-0.02.58.21.wav'
[2025-05-21T18:26:35.626Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] Lycoris Recoil - Friends Are Thieves of Time - 06 [1080p CR WEB-DL AVC AAC][MultiSub][81258F5B]\segment_0.00.21.95-0.00.24.67.wav: ENOENT: no such file or directory, unlink 'D:\Projekty\splendour.cafe-bot - v2\app\audio_processing\[Erai-raws] Lycoris Recoil - Friends Are Thieves of Time - 06 [1080p CR WEB-DL AVC AAC][MultiSub][81258F5B]\segment_0.00.21.95-0.00.24.67.wav'
[2025-05-26T14:31:45.160Z] [31m[ERROR] Failed to create segment for 0:08:02.96-0:08:02.96: Failed to split audio segment: Command failed: ffmpeg -i "app\audio_processing\[Erai-raws] Uma Musume - Cinderella Gray - 08 [1080p AMZN WEB-DL AVC EAC3][MultiSub][CD7548E8]\full.wav" -ss 0:08:02.96 -to 0:08:02.96 -acodec copy "app\audio_processing\[Erai-raws] Uma Musume - Cinderella Gray - 08 [1080p AMZN WEB-DL AVC EAC3][MultiSub][CD7548E8]\segment_0.08.02.96-0.08.02.96.wav" -loglevel warning -stats -y
[aist#0:0/pcm_s16le @ 000001647e44cb80] Guessed Channel Layout: stereo
[out#0 @ 000001647e44cd40] -to value smaller than -ss; aborting.
Error opening output file app\audio_processing\[Erai-raws] Uma Musume - Cinderella Gray - 08 [1080p AMZN WEB-DL AVC EAC3][MultiSub][CD7548E8]\segment_0.08.02.96-0.08.02.96.wav.
Error opening output files: Invalid argument

[2025-05-26T14:31:45.209Z] [31m[ERROR] Failed to create segment for 0:10:06.89-0:10:06.89: Failed to split audio segment: Command failed: ffmpeg -i "app\audio_processing\[Erai-raws] Uma Musume - Cinderella Gray - 08 [1080p AMZN WEB-DL AVC EAC3][MultiSub][CD7548E8]\full.wav" -ss 0:10:06.89 -to 0:10:06.89 -acodec copy "app\audio_processing\[Erai-raws] Uma Musume - Cinderella Gray - 08 [1080p AMZN WEB-DL AVC EAC3][MultiSub][CD7548E8]\segment_0.10.06.89-0.10.06.89.wav" -loglevel warning -stats -y
[aist#0:0/pcm_s16le @ 000001c153d2cb80] Guessed Channel Layout: stereo
[out#0 @ 000001c153d2cd40] -to value smaller than -ss; aborting.
Error opening output file app\audio_processing\[Erai-raws] Uma Musume - Cinderella Gray - 08 [1080p AMZN WEB-DL AVC EAC3][MultiSub][CD7548E8]\segment_0.10.06.89-0.10.06.89.wav.
Error opening output files: Invalid argument

[2025-05-26T14:31:45.335Z] [31m[ERROR] Failed to create segment for 0:17:59.44-0:17:59.44: Failed to split audio segment: Command failed: ffmpeg -i "app\audio_processing\[Erai-raws] Uma Musume - Cinderella Gray - 08 [1080p AMZN WEB-DL AVC EAC3][MultiSub][CD7548E8]\full.wav" -ss 0:17:59.44 -to 0:17:59.44 -acodec copy "app\audio_processing\[Erai-raws] Uma Musume - Cinderella Gray - 08 [1080p AMZN WEB-DL AVC EAC3][MultiSub][CD7548E8]\segment_0.17.59.44-0.17.59.44.wav" -loglevel warning -stats -y
[aist#0:0/pcm_s16le @ 0000026030edcb80] Guessed Channel Layout: stereo
[out#0 @ 0000026030edcd40] -to value smaller than -ss; aborting.
Error opening output file app\audio_processing\[Erai-raws] Uma Musume - Cinderella Gray - 08 [1080p AMZN WEB-DL AVC EAC3][MultiSub][CD7548E8]\segment_0.17.59.44-0.17.59.44.wav.
Error opening output files: Invalid argument

[2025-05-26T14:31:45.338Z] [31m[ERROR] Failed to create segment for 0:17:57.83-0:17:57.83: Failed to split audio segment: Command failed: ffmpeg -i "app\audio_processing\[Erai-raws] Uma Musume - Cinderella Gray - 08 [1080p AMZN WEB-DL AVC EAC3][MultiSub][CD7548E8]\full.wav" -ss 0:17:57.83 -to 0:17:57.83 -acodec copy "app\audio_processing\[Erai-raws] Uma Musume - Cinderella Gray - 08 [1080p AMZN WEB-DL AVC EAC3][MultiSub][CD7548E8]\segment_0.17.57.83-0.17.57.83.wav" -loglevel warning -stats -y
[aist#0:0/pcm_s16le @ 0000018f86a5cb80] Guessed Channel Layout: stereo
[out#0 @ 0000018f86a5cd40] -to value smaller than -ss; aborting.
Error opening output file app\audio_processing\[Erai-raws] Uma Musume - Cinderella Gray - 08 [1080p AMZN WEB-DL AVC EAC3][MultiSub][CD7548E8]\segment_0.17.57.83-0.17.57.83.wav.
Error opening output files: Invalid argument

[2025-05-26T14:31:45.403Z] [31m[ERROR] Failed to create segment for 0:19:10.12-0:19:10.12: Failed to split audio segment: Command failed: ffmpeg -i "app\audio_processing\[Erai-raws] Uma Musume - Cinderella Gray - 08 [1080p AMZN WEB-DL AVC EAC3][MultiSub][CD7548E8]\full.wav" -ss 0:19:10.12 -to 0:19:10.12 -acodec copy "app\audio_processing\[Erai-raws] Uma Musume - Cinderella Gray - 08 [1080p AMZN WEB-DL AVC EAC3][MultiSub][CD7548E8]\segment_0.19.10.12-0.19.10.12.wav" -loglevel warning -stats -y
[aist#0:0/pcm_s16le @ 00000155510bcb80] Guessed Channel Layout: stereo
[out#0 @ 00000155510bcd40] -to value smaller than -ss; aborting.
Error opening output file app\audio_processing\[Erai-raws] Uma Musume - Cinderella Gray - 08 [1080p AMZN WEB-DL AVC EAC3][MultiSub][CD7548E8]\segment_0.19.10.12-0.19.10.12.wav.
Error opening output files: Invalid argument

[2025-05-26T14:46:36.436Z] [31m[ERROR] Error in replacing process: Command failed: node app/3replace/1separateActors.js
[2025-05-30T22:24:18.175Z] Error: ENOENT: no such file or directory, rename 'C:\Users\<USER>\Desktop\lycoris.cafe - bot\app\0rss\downloads\[Erai-raws] Enen no Shouboutai - San no Shou - 09 [1080p CR WEB-DL AVC AAC][MultiSub][0BF28890].mkv' -> 'C:\Users\<USER>\Desktop\lycoris.cafe - bot\app\0rss\downloads\[Erai-raws] Enen no Shouboutai - San no Shou - 09 [1080p CR WEB-DL AVC AAC][MultiSub][0BF28890].mkv'
[2025-05-30T22:24:18.499Z] [2025-05-30T22:24:18.499Z] Error: ENOENT: no such file or directory, rename 'C:\Users\<USER>\Desktop\lycoris.cafe - bot\app\0rss\downloads\[Erai-raws] Enen no Shouboutai - San no Shou - 09 [1080p CR WEB-DL AVC AAC][MultiSub][0BF28890].mkv' -> 'C:\Users\<USER>\Desktop\lycoris.cafe - bot\app\0rss\downloads\[Erai-raws] Enen no Shouboutai - San no Shou - 09 [1080p CR WEB-DL AVC AAC][MultiSub][0BF28890].mkv'
    at Object.renameSync (node:fs:1020:11)
    at Timeout._onTimeout (file:///C:/Users/<USER>/Desktop/lycoris.cafe%20-%20bot/app/0rss/download.js:309:14)
    at listOnTimeout (node:internal/timers:594:17)
    at process.processTimers (node:internal/timers:529:7)

[2025-05-31T09:47:28.388Z] (node:14192) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-05-31T10:51:35.008Z] (node:16916) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-05-31T13:11:19.822Z] (node:676) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-05-31T13:26:15.238Z] (node:15944) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-05-31T16:46:30.544Z] (node:13668) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-05-31T17:26:42.277Z] (node:12352) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-05-31T18:10:53.598Z] (node:15564) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-05-31T18:22:05.874Z] (node:8860) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-05-31T18:55:21.917Z] [ERROR] Attempt 1: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T19:00:21.623Z] [ERROR] Attempt 1: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T19:00:52.718Z] [ERROR] Attempt 2: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T19:01:23.828Z] [ERROR] Attempt 3: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T19:01:23.828Z] [ERROR] Giving up after 3 attempts
[2025-05-31T19:01:23.843Z] Error: Failed after 3 attempts: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T19:01:24.200Z] [2025-05-31T19:01:24.200Z]
Error: Failed after 3 attempts: connect ETIMEDOUT 186.2.163.20:443
    at fetchRSS (file:///C:/Users/<USER>/Desktop/lycoris.cafe%20-%20bot/app/0rss/download.js:719:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async processFeed (file:///C:/Users/<USER>/Desktop/lycoris.cafe%20-%20bot/app/0rss/download.js:369:16)

[2025-05-31T19:01:59.404Z] [ERROR] Attempt 1: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T19:10:21.237Z] [ERROR] Attempt 1: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T19:10:52.326Z] [ERROR] Attempt 2: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T19:11:23.402Z] [ERROR] Attempt 3: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T19:11:23.402Z] [ERROR] Giving up after 3 attempts
[2025-05-31T19:11:23.402Z] Error: Failed after 3 attempts: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T19:11:23.818Z] [2025-05-31T19:11:23.818Z]
Error: Failed after 3 attempts: connect ETIMEDOUT 186.2.163.20:443
    at fetchRSS (file:///C:/Users/<USER>/Desktop/lycoris.cafe%20-%20bot/app/0rss/download.js:719:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async processFeed (file:///C:/Users/<USER>/Desktop/lycoris.cafe%20-%20bot/app/0rss/download.js:369:16)

[2025-05-31T19:11:57.431Z] [ERROR] Attempt 1: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T19:12:28.477Z] [ERROR] Attempt 2: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T19:12:59.586Z] [ERROR] Attempt 3: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T19:12:59.586Z] [ERROR] Giving up after 3 attempts
[2025-05-31T19:12:59.586Z] Error: Failed after 3 attempts: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T19:12:59.885Z] [2025-05-31T19:12:59.885Z]
Error: Failed after 3 attempts: connect ETIMEDOUT 186.2.163.20:443
    at fetchRSS (file:///C:/Users/<USER>/Desktop/lycoris.cafe%20-%20bot/app/0rss/download.js:719:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async processFeed (file:///C:/Users/<USER>/Desktop/lycoris.cafe%20-%20bot/app/0rss/download.js:369:16)

[2025-05-31T19:13:33.193Z] [ERROR] Attempt 1: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T19:14:04.287Z] [ERROR] Attempt 2: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T19:14:35.379Z] [ERROR] Attempt 3: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T19:14:35.379Z] [ERROR] Giving up after 3 attempts
[2025-05-31T19:14:35.379Z] Error: Failed after 3 attempts: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T19:14:35.893Z] [2025-05-31T19:14:35.893Z]
Error: Failed after 3 attempts: connect ETIMEDOUT 186.2.163.20:443
    at fetchRSS (file:///C:/Users/<USER>/Desktop/lycoris.cafe%20-%20bot/app/0rss/download.js:719:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async processFeed (file:///C:/Users/<USER>/Desktop/lycoris.cafe%20-%20bot/app/0rss/download.js:369:16)

[2025-05-31T19:15:09.580Z] [ERROR] Attempt 1: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T19:15:21.627Z] [ERROR] Attempt 1: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T19:15:40.658Z] [ERROR] Attempt 2: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T19:15:52.674Z] [ERROR] Attempt 2: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T19:16:11.750Z] [ERROR] Attempt 3: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T19:16:11.750Z] [ERROR] Giving up after 3 attempts
[2025-05-31T19:16:11.750Z] Error: Failed after 3 attempts: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T19:16:12.046Z] [2025-05-31T19:16:12.046Z]
Error: Failed after 3 attempts: connect ETIMEDOUT 186.2.163.20:443
    at fetchRSS (file:///C:/Users/<USER>/Desktop/lycoris.cafe%20-%20bot/app/0rss/download.js:719:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async processFeed (file:///C:/Users/<USER>/Desktop/lycoris.cafe%20-%20bot/app/0rss/download.js:369:16)

[2025-05-31T19:16:45.250Z] [ERROR] Attempt 1: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T19:17:16.327Z] [ERROR] Attempt 2: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T19:20:21.243Z] [ERROR] Attempt 1: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T19:20:52.320Z] [ERROR] Attempt 2: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T19:21:23.414Z] [ERROR] Attempt 3: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T19:21:23.414Z] [ERROR] Giving up after 3 attempts
[2025-05-31T19:21:23.414Z] Error: Failed after 3 attempts: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T19:21:23.746Z] [2025-05-31T19:21:23.746Z]
Error: Failed after 3 attempts: connect ETIMEDOUT 186.2.163.20:443
    at fetchRSS (file:///C:/Users/<USER>/Desktop/lycoris.cafe%20-%20bot/app/0rss/download.js:719:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async processFeed (file:///C:/Users/<USER>/Desktop/lycoris.cafe%20-%20bot/app/0rss/download.js:369:16)

[2025-05-31T19:21:56.866Z] [ERROR] Attempt 1: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T20:30:21.609Z] [ERROR] Attempt 1: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T20:30:52.687Z] [ERROR] Attempt 2: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T20:31:23.794Z] [ERROR] Attempt 3: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T20:31:23.794Z] [ERROR] Giving up after 3 attempts
[2025-05-31T20:31:23.811Z] Error: Failed after 3 attempts: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T20:31:24.277Z] [2025-05-31T20:31:24.277Z]
Error: Failed after 3 attempts: connect ETIMEDOUT 186.2.163.20:443
    at fetchRSS (file:///C:/Users/<USER>/Desktop/lycoris.cafe%20-%20bot/app/0rss/download.js:719:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async processFeed (file:///C:/Users/<USER>/Desktop/lycoris.cafe%20-%20bot/app/0rss/download.js:369:16)

[2025-05-31T20:31:59.950Z] [ERROR] Attempt 1: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T20:32:31.012Z] [ERROR] Attempt 2: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T20:33:02.074Z] [ERROR] Attempt 3: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T20:33:02.074Z] [ERROR] Giving up after 3 attempts
[2025-05-31T20:33:02.074Z] Error: Failed after 3 attempts: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T20:33:02.463Z] [2025-05-31T20:33:02.463Z]
Error: Failed after 3 attempts: connect ETIMEDOUT 186.2.163.20:443
    at fetchRSS (file:///C:/Users/<USER>/Desktop/lycoris.cafe%20-%20bot/app/0rss/download.js:719:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async processFeed (file:///C:/Users/<USER>/Desktop/lycoris.cafe%20-%20bot/app/0rss/download.js:369:16)

[2025-05-31T20:33:36.243Z] [ERROR] Attempt 1: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T20:34:07.337Z] [ERROR] Attempt 2: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T20:34:38.428Z] [ERROR] Attempt 3: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T20:34:38.428Z] [ERROR] Giving up after 3 attempts
[2025-05-31T20:34:38.428Z] Error: Failed after 3 attempts: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T20:34:38.802Z] [2025-05-31T20:34:38.802Z]
Error: Failed after 3 attempts: connect ETIMEDOUT 186.2.163.20:443
    at fetchRSS (file:///C:/Users/<USER>/Desktop/lycoris.cafe%20-%20bot/app/0rss/download.js:719:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async processFeed (file:///C:/Users/<USER>/Desktop/lycoris.cafe%20-%20bot/app/0rss/download.js:369:16)

[2025-05-31T20:35:12.177Z] [ERROR] Attempt 1: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T20:35:21.256Z] [ERROR] Attempt 1: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T20:35:43.286Z] [ERROR] Attempt 2: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T20:35:52.349Z] [ERROR] Attempt 2: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T20:40:21.996Z] [ERROR] Attempt 1: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T20:40:53.073Z] [ERROR] Attempt 2: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T20:41:24.150Z] [ERROR] Attempt 3: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T20:41:24.150Z] [ERROR] Giving up after 3 attempts
[2025-05-31T20:41:24.150Z] Error: Failed after 3 attempts: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T20:41:24.519Z] [2025-05-31T20:41:24.519Z]
Error: Failed after 3 attempts: connect ETIMEDOUT 186.2.163.20:443
    at fetchRSS (file:///C:/Users/<USER>/Desktop/lycoris.cafe%20-%20bot/app/0rss/download.js:719:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async processFeed (file:///C:/Users/<USER>/Desktop/lycoris.cafe%20-%20bot/app/0rss/download.js:369:16)

[2025-05-31T20:41:57.914Z] [ERROR] Attempt 1: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T20:42:28.992Z] [ERROR] Attempt 2: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T20:43:00.085Z] [ERROR] Attempt 3: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T20:43:00.085Z] [ERROR] Giving up after 3 attempts
[2025-05-31T20:43:00.100Z] Error: Failed after 3 attempts: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T20:43:00.426Z] [2025-05-31T20:43:00.426Z]
Error: Failed after 3 attempts: connect ETIMEDOUT 186.2.163.20:443
    at fetchRSS (file:///C:/Users/<USER>/Desktop/lycoris.cafe%20-%20bot/app/0rss/download.js:719:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async processFeed (file:///C:/Users/<USER>/Desktop/lycoris.cafe%20-%20bot/app/0rss/download.js:369:16)

[2025-05-31T20:43:34.022Z] [ERROR] Attempt 1: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T20:44:05.098Z] [ERROR] Attempt 2: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T20:44:36.176Z] [ERROR] Attempt 3: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T20:44:36.176Z] [ERROR] Giving up after 3 attempts
[2025-05-31T20:44:36.176Z] Error: Failed after 3 attempts: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T20:44:36.436Z] [2025-05-31T20:44:36.436Z]
Error: Failed after 3 attempts: connect ETIMEDOUT 186.2.163.20:443
    at fetchRSS (file:///C:/Users/<USER>/Desktop/lycoris.cafe%20-%20bot/app/0rss/download.js:719:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async processFeed (file:///C:/Users/<USER>/Desktop/lycoris.cafe%20-%20bot/app/0rss/download.js:369:16)

[2025-05-31T20:45:09.861Z] [ERROR] Attempt 1: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T20:45:21.892Z] [ERROR] Attempt 1: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T20:50:21.915Z] [ERROR] Attempt 1: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T20:50:52.991Z] [ERROR] Attempt 2: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T20:51:24.052Z] [ERROR] Attempt 3: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T20:51:24.052Z] [ERROR] Giving up after 3 attempts
[2025-05-31T20:51:24.067Z] Error: Failed after 3 attempts: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T20:51:24.532Z] [2025-05-31T20:51:24.532Z]
Error: Failed after 3 attempts: connect ETIMEDOUT 186.2.163.20:443
    at fetchRSS (file:///C:/Users/<USER>/Desktop/lycoris.cafe%20-%20bot/app/0rss/download.js:719:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async processFeed (file:///C:/Users/<USER>/Desktop/lycoris.cafe%20-%20bot/app/0rss/download.js:369:16)

[2025-05-31T20:52:00.272Z] [ERROR] Attempt 1: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T20:52:31.380Z] [ERROR] Attempt 2: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T20:53:02.456Z] [ERROR] Attempt 3: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T20:53:02.456Z] [ERROR] Giving up after 3 attempts
[2025-05-31T20:53:02.456Z] Error: Failed after 3 attempts: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T20:53:02.879Z] [2025-05-31T20:53:02.879Z]
Error: Failed after 3 attempts: connect ETIMEDOUT 186.2.163.20:443
    at fetchRSS (file:///C:/Users/<USER>/Desktop/lycoris.cafe%20-%20bot/app/0rss/download.js:719:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async processFeed (file:///C:/Users/<USER>/Desktop/lycoris.cafe%20-%20bot/app/0rss/download.js:369:16)

[2025-05-31T20:53:36.190Z] [ERROR] Attempt 1: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T21:00:21.487Z] [ERROR] Attempt 1: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T21:00:52.566Z] [ERROR] Attempt 2: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T21:01:23.675Z] [ERROR] Attempt 3: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T21:01:23.675Z] [ERROR] Giving up after 3 attempts
[2025-05-31T21:01:23.675Z] Error: Failed after 3 attempts: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T21:01:23.981Z] [2025-05-31T21:01:23.981Z]
Error: Failed after 3 attempts: connect ETIMEDOUT 186.2.163.20:443
    at fetchRSS (file:///C:/Users/<USER>/Desktop/lycoris.cafe%20-%20bot/app/0rss/download.js:719:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async processFeed (file:///C:/Users/<USER>/Desktop/lycoris.cafe%20-%20bot/app/0rss/download.js:369:16)

[2025-05-31T21:01:57.440Z] [ERROR] Attempt 1: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T21:02:28.548Z] [ERROR] Attempt 2: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T21:02:59.656Z] [ERROR] Attempt 3: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T21:02:59.656Z] [ERROR] Giving up after 3 attempts
[2025-05-31T21:02:59.656Z] Error: Failed after 3 attempts: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T21:03:00.101Z] [2025-05-31T21:03:00.101Z]
Error: Failed after 3 attempts: connect ETIMEDOUT 186.2.163.20:443
    at fetchRSS (file:///C:/Users/<USER>/Desktop/lycoris.cafe%20-%20bot/app/0rss/download.js:719:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async processFeed (file:///C:/Users/<USER>/Desktop/lycoris.cafe%20-%20bot/app/0rss/download.js:369:16)

[2025-05-31T21:03:33.375Z] [ERROR] Attempt 1: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T21:04:04.467Z] [ERROR] Attempt 2: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T21:04:35.544Z] [ERROR] Attempt 3: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T21:04:35.544Z] [ERROR] Giving up after 3 attempts
[2025-05-31T21:04:35.544Z] Error: Failed after 3 attempts: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T21:04:36.034Z] [2025-05-31T21:04:36.034Z]
Error: Failed after 3 attempts: connect ETIMEDOUT 186.2.163.20:443
    at fetchRSS (file:///C:/Users/<USER>/Desktop/lycoris.cafe%20-%20bot/app/0rss/download.js:719:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async processFeed (file:///C:/Users/<USER>/Desktop/lycoris.cafe%20-%20bot/app/0rss/download.js:369:16)

[2025-05-31T21:05:09.292Z] [ERROR] Attempt 1: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T21:05:21.293Z] [ERROR] Attempt 1: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T21:05:40.369Z] [ERROR] Attempt 2: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T21:05:52.401Z] [ERROR] Attempt 2: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T21:06:11.431Z] [ERROR] Attempt 3: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T21:06:11.431Z] [ERROR] Giving up after 3 attempts
[2025-05-31T21:06:11.431Z] Error: Failed after 3 attempts: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T21:06:11.962Z] [2025-05-31T21:06:11.962Z]
Error: Failed after 3 attempts: connect ETIMEDOUT 186.2.163.20:443
    at fetchRSS (file:///C:/Users/<USER>/Desktop/lycoris.cafe%20-%20bot/app/0rss/download.js:719:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async processFeed (file:///C:/Users/<USER>/Desktop/lycoris.cafe%20-%20bot/app/0rss/download.js:369:16)

[2025-05-31T21:06:45.102Z] [ERROR] Attempt 1: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T21:07:16.211Z] [ERROR] Attempt 2: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T21:07:47.302Z] [ERROR] Attempt 3: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T21:07:47.302Z] [ERROR] Giving up after 3 attempts
[2025-05-31T21:07:47.302Z] Error: Failed after 3 attempts: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T21:07:47.790Z] [2025-05-31T21:07:47.790Z]
Error: Failed after 3 attempts: connect ETIMEDOUT 186.2.163.20:443
    at fetchRSS (file:///C:/Users/<USER>/Desktop/lycoris.cafe%20-%20bot/app/0rss/download.js:719:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async processFeed (file:///C:/Users/<USER>/Desktop/lycoris.cafe%20-%20bot/app/0rss/download.js:369:16)

[2025-05-31T21:08:20.755Z] [ERROR] Attempt 1: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T21:08:51.864Z] [ERROR] Attempt 2: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T21:09:22.925Z] [ERROR] Attempt 3: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T21:09:22.925Z] [ERROR] Giving up after 3 attempts
[2025-05-31T21:09:22.925Z] Error: Failed after 3 attempts: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T21:09:23.362Z] [2025-05-31T21:09:23.362Z]
Error: Failed after 3 attempts: connect ETIMEDOUT 186.2.163.20:443
    at fetchRSS (file:///C:/Users/<USER>/Desktop/lycoris.cafe%20-%20bot/app/0rss/download.js:719:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async processFeed (file:///C:/Users/<USER>/Desktop/lycoris.cafe%20-%20bot/app/0rss/download.js:369:16)

[2025-05-31T21:09:56.423Z] [ERROR] Attempt 1: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T21:10:21.579Z] [ERROR] Attempt 1: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T21:10:27.516Z] [ERROR] Attempt 2: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T21:10:52.672Z] [ERROR] Attempt 2: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T21:10:58.609Z] [ERROR] Attempt 3: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T21:10:58.609Z] [ERROR] Giving up after 3 attempts
[2025-05-31T21:10:58.609Z] Error: Failed after 3 attempts: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T21:10:58.999Z] [2025-05-31T21:10:58.999Z]
Error: Failed after 3 attempts: connect ETIMEDOUT 186.2.163.20:443
    at fetchRSS (file:///C:/Users/<USER>/Desktop/lycoris.cafe%20-%20bot/app/0rss/download.js:719:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async processFeed (file:///C:/Users/<USER>/Desktop/lycoris.cafe%20-%20bot/app/0rss/download.js:369:16)

[2025-05-31T23:55:21.589Z] [ERROR] Attempt 1: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-05-31T23:55:52.701Z] [ERROR] Attempt 2: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-06-01T09:44:54.664Z] (node:4000) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-06-01T11:20:32.853Z] [ERROR] Attempt 1: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-06-01T11:21:14.996Z] [ERROR] Attempt 2: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-06-01T11:21:57.101Z] [ERROR] Attempt 3: Failed to fetch RSS feed: connect ETIMEDOUT 186.2.163.20:443
[2025-06-01T11:21:57.101Z] [ERROR] Giving up after 3 attempts
[2025-06-01T11:21:57.101Z] Error: Failed after 3 attempts: connect ETIMEDOUT 186.2.163.20:443
[2025-06-01T11:22:08.226Z] Error sending Discord webhook: Error: getaddrinfo ENOTFOUND discord.com
[2025-06-01T14:36:57.917Z] (node:15512) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-06-01T15:21:55.868Z] (node:12940) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-06-01T16:13:58.101Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] aiomodarkelf - 09 [1080p HIDIVE WEB-DL AVC AAC][8C6AC266]\segment_0.03.18.83-0.03.21.83.wav: ENOENT: no such file or directory, unlink 'C:\Users\<USER>\Desktop\lycoris.cafe - bot\app\audio_processing\[Erai-raws] aiomodarkelf - 09 [1080p HIDIVE WEB-DL AVC AAC][8C6AC266]\segment_0.03.18.83-0.03.21.83.wav'
[2025-06-01T16:13:59.575Z] (node:12504) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-06-01T16:24:15.064Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] aiomodarkelf - 09 [1080p ADN WEB-DL AVC AAC][MultiSub][A3F9727A]\segment_0.03.22.93-0.03.25.93.wav: ENOENT: no such file or directory, unlink 'C:\Users\<USER>\Desktop\lycoris.cafe - bot\app\audio_processing\[Erai-raws] aiomodarkelf - 09 [1080p ADN WEB-DL AVC AAC][MultiSub][A3F9727A]\segment_0.03.22.93-0.03.25.93.wav'
[2025-06-01T16:24:16.390Z] (node:4316) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-06-01T16:29:19.545Z] [31m[ERROR] Error translating files: [Erai-raws] aiomodarkelf - 09 [1080p ADN WEB-DL AVC AAC][MultiSub][A3F9727A]_eng.txt  Error: Translation failed: Anthropic API returned a falsy value. null
[2025-06-01T16:29:19.601Z] [31m[ERROR] Error in translating process: Command failed: node app/2translate/translate.js
[2025-06-02T13:26:36.302Z] (node:8480) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-06-02T13:41:49.498Z] (node:10264) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-06-02T15:18:25.522Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] Kijin Gentoushou - 10 [1080p HIDIVE WEB-DL AVC AAC][ACEDCB06]\segment_0.00.50.33-0.00.56.38.wav: ENOENT: no such file or directory, unlink 'C:\Users\<USER>\Desktop\lycoris.cafe - bot\app\audio_processing\[Erai-raws] Kijin Gentoushou - 10 [1080p HIDIVE WEB-DL AVC AAC][ACEDCB06]\segment_0.00.50.33-0.00.56.38.wav'
[2025-06-02T15:18:25.522Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] Kijin Gentoushou - 10 [1080p HIDIVE WEB-DL AVC AAC][ACEDCB06]\segment_0.00.41.88-0.00.44.33.wav: ENOENT: no such file or directory, unlink 'C:\Users\<USER>\Desktop\lycoris.cafe - bot\app\audio_processing\[Erai-raws] Kijin Gentoushou - 10 [1080p HIDIVE WEB-DL AVC AAC][ACEDCB06]\segment_0.00.41.88-0.00.44.33.wav'
[2025-06-02T15:18:25.522Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] Kijin Gentoushou - 10 [1080p HIDIVE WEB-DL AVC AAC][ACEDCB06]\segment_0.00.11.34-0.00.14.25.wav: ENOENT: no such file or directory, unlink 'C:\Users\<USER>\Desktop\lycoris.cafe - bot\app\audio_processing\[Erai-raws] Kijin Gentoushou - 10 [1080p HIDIVE WEB-DL AVC AAC][ACEDCB06]\segment_0.00.11.34-0.00.14.25.wav'
[2025-06-02T15:18:25.522Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] Kijin Gentoushou - 10 [1080p HIDIVE WEB-DL AVC AAC][ACEDCB06]\segment_0.00.11.34-0.00.14.25.wav: ENOENT: no such file or directory, unlink 'C:\Users\<USER>\Desktop\lycoris.cafe - bot\app\audio_processing\[Erai-raws] Kijin Gentoushou - 10 [1080p HIDIVE WEB-DL AVC AAC][ACEDCB06]\segment_0.00.11.34-0.00.14.25.wav'
[2025-06-02T15:18:25.522Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] Kijin Gentoushou - 10 [1080p HIDIVE WEB-DL AVC AAC][ACEDCB06]\segment_0.00.11.34-0.00.14.25.wav: ENOENT: no such file or directory, unlink 'C:\Users\<USER>\Desktop\lycoris.cafe - bot\app\audio_processing\[Erai-raws] Kijin Gentoushou - 10 [1080p HIDIVE WEB-DL AVC AAC][ACEDCB06]\segment_0.00.11.34-0.00.14.25.wav'
[2025-06-02T15:18:25.522Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] Kijin Gentoushou - 10 [1080p HIDIVE WEB-DL AVC AAC][ACEDCB06]\segment_0.00.11.34-0.00.14.25.wav: ENOENT: no such file or directory, unlink 'C:\Users\<USER>\Desktop\lycoris.cafe - bot\app\audio_processing\[Erai-raws] Kijin Gentoushou - 10 [1080p HIDIVE WEB-DL AVC AAC][ACEDCB06]\segment_0.00.11.34-0.00.14.25.wav'
[2025-06-02T15:18:25.522Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] Kijin Gentoushou - 10 [1080p HIDIVE WEB-DL AVC AAC][ACEDCB06]\segment_0.00.11.33-0.00.14.25.wav: ENOENT: no such file or directory, unlink 'C:\Users\<USER>\Desktop\lycoris.cafe - bot\app\audio_processing\[Erai-raws] Kijin Gentoushou - 10 [1080p HIDIVE WEB-DL AVC AAC][ACEDCB06]\segment_0.00.11.33-0.00.14.25.wav'
[2025-06-02T15:18:34.472Z] (node:12912) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-06-02T15:30:23.920Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] Kijin Gentoushou - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][379EA2BA]\segment_0.00.11.34-0.00.14.25.wav: ENOENT: no such file or directory, unlink 'C:\Users\<USER>\Desktop\lycoris.cafe - bot\app\audio_processing\[Erai-raws] Kijin Gentoushou - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][379EA2BA]\segment_0.00.11.34-0.00.14.25.wav'
[2025-06-02T15:30:23.920Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] Kijin Gentoushou - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][379EA2BA]\segment_0.00.11.34-0.00.14.25.wav: ENOENT: no such file or directory, unlink 'C:\Users\<USER>\Desktop\lycoris.cafe - bot\app\audio_processing\[Erai-raws] Kijin Gentoushou - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][379EA2BA]\segment_0.00.11.34-0.00.14.25.wav'
[2025-06-02T15:30:23.920Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] Kijin Gentoushou - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][379EA2BA]\segment_0.00.11.33-0.00.14.25.wav: ENOENT: no such file or directory, unlink 'C:\Users\<USER>\Desktop\lycoris.cafe - bot\app\audio_processing\[Erai-raws] Kijin Gentoushou - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][379EA2BA]\segment_0.00.11.33-0.00.14.25.wav'
[2025-06-02T15:30:23.936Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] Kijin Gentoushou - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][379EA2BA]\segment_0.00.11.34-0.00.14.25.wav: ENOENT: no such file or directory, unlink 'C:\Users\<USER>\Desktop\lycoris.cafe - bot\app\audio_processing\[Erai-raws] Kijin Gentoushou - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][379EA2BA]\segment_0.00.11.34-0.00.14.25.wav'
[2025-06-02T15:30:23.936Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] Kijin Gentoushou - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][379EA2BA]\segment_0.00.41.88-0.00.44.33.wav: ENOENT: no such file or directory, unlink 'C:\Users\<USER>\Desktop\lycoris.cafe - bot\app\audio_processing\[Erai-raws] Kijin Gentoushou - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][379EA2BA]\segment_0.00.41.88-0.00.44.33.wav'
[2025-06-02T15:30:23.936Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] Kijin Gentoushou - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][379EA2BA]\segment_0.00.11.34-0.00.14.25.wav: ENOENT: no such file or directory, unlink 'C:\Users\<USER>\Desktop\lycoris.cafe - bot\app\audio_processing\[Erai-raws] Kijin Gentoushou - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][379EA2BA]\segment_0.00.11.34-0.00.14.25.wav'
[2025-06-02T15:30:23.952Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] Kijin Gentoushou - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][379EA2BA]\segment_0.00.50.33-0.00.56.38.wav: ENOENT: no such file or directory, unlink 'C:\Users\<USER>\Desktop\lycoris.cafe - bot\app\audio_processing\[Erai-raws] Kijin Gentoushou - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][379EA2BA]\segment_0.00.50.33-0.00.56.38.wav'
[2025-06-02T15:30:33.504Z] (node:9664) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-06-02T17:43:25.936Z] [31m[ERROR] Failed to create segment for 0:03:31.45-0:03:31.45: Failed to split audio segment: Command failed: ffmpeg -i "app\audio_processing\The.Mononoke.Lecture.Logs.of.Chuzenji-sensei.S01E09.Rumor.of.the.Haunted.House.1080p.B-Global.WEB-DL.JPN.AAC2.0.H.264.MSubs-ToonsHub\full.wav" -ss 0:03:31.45 -to 0:03:31.45 -acodec copy "app\audio_processing\The.Mononoke.Lecture.Logs.of.Chuzenji-sensei.S01E09.Rumor.of.the.Haunted.House.1080p.B-Global.WEB-DL.JPN.AAC2.0.H.264.MSubs-ToonsHub\segment_0.03.31.45-0.03.31.45.wav" -loglevel warning -stats -y
[aist#0:0/pcm_s16le @ 000001fef80376c0] Guessed Channel Layout: stereo
[out#0 @ 000001fef803f900] -to value smaller than -ss; aborting.
Error opening output file app\audio_processing\The.Mononoke.Lecture.Logs.of.Chuzenji-sensei.S01E09.Rumor.of.the.Haunted.House.1080p.B-Global.WEB-DL.JPN.AAC2.0.H.264.MSubs-ToonsHub\segment_0.03.31.45-0.03.31.45.wav.
Error opening output files: Invalid argument

[2025-06-02T17:46:41.911Z] (node:16536) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-06-03T14:11:00.352Z] (node:12764) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-06-03T15:11:03.803Z] (node:11740) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-06-03T15:14:03.778Z] [31m[ERROR] Error translating files: [Erai-raws] Aru Majo ga Shinu Made - 10 [1080p CR WEB-DL AVC AAC][MultiSub][ECAB3332]_eng.txt  Error: [31m[ERROR] Line mismatch encountered during translations. Ending the job after 5 attempts.
[2025-06-03T15:14:03.944Z] [31m[ERROR] Error in translating process: Command failed: node app/2translate/translate.js
[2025-06-03T16:29:36.967Z] (node:12768) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-06-03T16:41:05.445Z] (node:11596) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-06-03T19:10:50.569Z] (node:936) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-06-04T15:25:55.185Z] (node:12572) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-06-04T15:40:57.172Z] (node:16560) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-06-04T17:35:55.987Z] (node:17292) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-06-05T13:06:53.331Z] (node:16536) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-06-05T13:15:57.151Z] [31m[ERROR] Error translating files: [Erai-raws] Ninja to Koroshiya no Futarigurashi - 09 [1080p CR WEB-DL AVC AAC][MultiSub][DEAE531B]_eng.txt  Error: Translation failed: Anthropic API returned a falsy value. null
[2025-06-05T13:15:57.214Z] [31m[ERROR] Error in translating process: Command failed: node app/2translate/translate.js
[2025-06-05T15:10:57.617Z] (node:11232) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-06-05T15:16:00.951Z] [31m[ERROR] Error translating files: [Erai-raws] yamihealer - 10 [1080p CR WEB-DL AVC AAC][MultiSub][DCB013BF]_eng.txt  Error: Translation failed: Anthropic API returned a falsy value. null
[2025-06-05T15:16:01.114Z] [31m[ERROR] Error in translating process: Command failed: node app/2translate/translate.js
[2025-06-05T16:12:17.826Z] (node:3476) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-06-05T16:30:13.874Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] Rock wa Lady no Tashinami deshite - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][4ED8C7E3]\segment_0.04.56.96-0.04.58.92.wav: ENOENT: no such file or directory, unlink 'C:\Users\<USER>\Desktop\lycoris.cafe - bot\app\audio_processing\[Erai-raws] Rock wa Lady no Tashinami deshite - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][4ED8C7E3]\segment_0.04.56.96-0.04.58.92.wav'
[2025-06-05T16:30:13.983Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] Rock wa Lady no Tashinami deshite - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][4ED8C7E3]\segment_0.17.29.38-0.17.31.88.wav: ENOENT: no such file or directory, unlink 'C:\Users\<USER>\Desktop\lycoris.cafe - bot\app\audio_processing\[Erai-raws] Rock wa Lady no Tashinami deshite - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][4ED8C7E3]\segment_0.17.29.38-0.17.31.88.wav'
[2025-06-05T16:30:13.999Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] Rock wa Lady no Tashinami deshite - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][4ED8C7E3]\segment_0.17.29.38-0.17.31.88.wav: ENOENT: no such file or directory, unlink 'C:\Users\<USER>\Desktop\lycoris.cafe - bot\app\audio_processing\[Erai-raws] Rock wa Lady no Tashinami deshite - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][4ED8C7E3]\segment_0.17.29.38-0.17.31.88.wav'
[2025-06-05T16:30:14.023Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] Rock wa Lady no Tashinami deshite - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][4ED8C7E3]\segment_0.23.36.00-0.23.37.04.wav: ENOENT: no such file or directory, unlink 'C:\Users\<USER>\Desktop\lycoris.cafe - bot\app\audio_processing\[Erai-raws] Rock wa Lady no Tashinami deshite - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][4ED8C7E3]\segment_0.23.36.00-0.23.37.04.wav'
[2025-06-05T16:34:00.959Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] Rock wa Lady no Tashinami deshite - 10 [1080p HIDIVE WEB-DL AVC AAC][C62F6615]\segment_0.04.56.96-0.04.58.92.wav: ENOENT: no such file or directory, unlink 'C:\Users\<USER>\Desktop\lycoris.cafe - bot\app\audio_processing\[Erai-raws] Rock wa Lady no Tashinami deshite - 10 [1080p HIDIVE WEB-DL AVC AAC][C62F6615]\segment_0.04.56.96-0.04.58.92.wav'
[2025-06-05T16:34:01.023Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] Rock wa Lady no Tashinami deshite - 10 [1080p HIDIVE WEB-DL AVC AAC][C62F6615]\segment_0.17.29.38-0.17.31.88.wav: ENOENT: no such file or directory, unlink 'C:\Users\<USER>\Desktop\lycoris.cafe - bot\app\audio_processing\[Erai-raws] Rock wa Lady no Tashinami deshite - 10 [1080p HIDIVE WEB-DL AVC AAC][C62F6615]\segment_0.17.29.38-0.17.31.88.wav'
[2025-06-05T16:34:01.023Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] Rock wa Lady no Tashinami deshite - 10 [1080p HIDIVE WEB-DL AVC AAC][C62F6615]\segment_0.17.29.38-0.17.31.88.wav: ENOENT: no such file or directory, unlink 'C:\Users\<USER>\Desktop\lycoris.cafe - bot\app\audio_processing\[Erai-raws] Rock wa Lady no Tashinami deshite - 10 [1080p HIDIVE WEB-DL AVC AAC][C62F6615]\segment_0.17.29.38-0.17.31.88.wav'
[2025-06-05T16:34:01.091Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] Rock wa Lady no Tashinami deshite - 10 [1080p HIDIVE WEB-DL AVC AAC][C62F6615]\segment_0.23.36.00-0.23.37.04.wav: ENOENT: no such file or directory, unlink 'C:\Users\<USER>\Desktop\lycoris.cafe - bot\app\audio_processing\[Erai-raws] Rock wa Lady no Tashinami deshite - 10 [1080p HIDIVE WEB-DL AVC AAC][C62F6615]\segment_0.23.36.00-0.23.37.04.wav'
[2025-06-05T16:34:12.865Z] (node:5660) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-06-05T16:34:14.803Z] [31m[ERROR] Error in translating process: Command failed: node app/2translate/translate.js
[2025-06-05T16:40:23.106Z] (node:1940) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-06-05T17:18:00.154Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] Rock wa Lady no Tashinami deshite - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][4ED8C7E3]\segment_0.04.56.96-0.04.58.92.wav: ENOENT: no such file or directory, unlink 'C:\Users\<USER>\Desktop\lycoris.cafe - bot\app\audio_processing\[Erai-raws] Rock wa Lady no Tashinami deshite - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][4ED8C7E3]\segment_0.04.56.96-0.04.58.92.wav'
[2025-06-05T17:18:00.326Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] Rock wa Lady no Tashinami deshite - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][4ED8C7E3]\segment_0.17.29.38-0.17.31.88.wav: ENOENT: no such file or directory, unlink 'C:\Users\<USER>\Desktop\lycoris.cafe - bot\app\audio_processing\[Erai-raws] Rock wa Lady no Tashinami deshite - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][4ED8C7E3]\segment_0.17.29.38-0.17.31.88.wav'
[2025-06-05T17:18:00.373Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] Rock wa Lady no Tashinami deshite - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][4ED8C7E3]\segment_0.17.29.38-0.17.31.88.wav: ENOENT: no such file or directory, unlink 'C:\Users\<USER>\Desktop\lycoris.cafe - bot\app\audio_processing\[Erai-raws] Rock wa Lady no Tashinami deshite - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][4ED8C7E3]\segment_0.17.29.38-0.17.31.88.wav'
[2025-06-05T17:18:00.379Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] Rock wa Lady no Tashinami deshite - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][4ED8C7E3]\segment_0.23.36.00-0.23.37.04.wav: ENOENT: no such file or directory, unlink 'C:\Users\<USER>\Desktop\lycoris.cafe - bot\app\audio_processing\[Erai-raws] Rock wa Lady no Tashinami deshite - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][4ED8C7E3]\segment_0.23.36.00-0.23.37.04.wav'
[2025-06-05T17:18:01.803Z] (node:8604) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-06-05T17:55:39.581Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] Hana wa Saku Shura no Gotoku - 08 [1080p HIDIVE WEB-DL AVC AAC][8596CC26]\segment_0.19.49.54-0.19.51.29.wav: ENOENT: no such file or directory, unlink 'C:\Users\<USER>\Desktop\lycoris.cafe - bot\app\audio_processing\[Erai-raws] Hana wa Saku Shura no Gotoku - 08 [1080p HIDIVE WEB-DL AVC AAC][8596CC26]\segment_0.19.49.54-0.19.51.29.wav'
[2025-06-05T17:55:39.584Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] Hana wa Saku Shura no Gotoku - 08 [1080p HIDIVE WEB-DL AVC AAC][8596CC26]\segment_0.17.35.33-0.17.37.33.wav: ENOENT: no such file or directory, unlink 'C:\Users\<USER>\Desktop\lycoris.cafe - bot\app\audio_processing\[Erai-raws] Hana wa Saku Shura no Gotoku - 08 [1080p HIDIVE WEB-DL AVC AAC][8596CC26]\segment_0.17.35.33-0.17.37.33.wav'
[2025-06-05T17:55:39.586Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] Hana wa Saku Shura no Gotoku - 08 [1080p HIDIVE WEB-DL AVC AAC][8596CC26]\segment_0.21.31.00-0.21.34.92.wav: ENOENT: no such file or directory, unlink 'C:\Users\<USER>\Desktop\lycoris.cafe - bot\app\audio_processing\[Erai-raws] Hana wa Saku Shura no Gotoku - 08 [1080p HIDIVE WEB-DL AVC AAC][8596CC26]\segment_0.21.31.00-0.21.34.92.wav'
[2025-06-05T17:55:42.183Z] (node:16148) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-06-05T18:05:17.232Z] (node:15384) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-06-05T19:03:06.626Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] Hana wa Saku Shura no Gotoku - 08 [1080p HIDIVE WEB-DL AVC AAC][8596CC26]\segment_0.19.49.54-0.19.51.29.wav: ENOENT: no such file or directory, unlink 'C:\Users\<USER>\Desktop\lycoris.cafe - bot\app\audio_processing\[Erai-raws] Hana wa Saku Shura no Gotoku - 08 [1080p HIDIVE WEB-DL AVC AAC][8596CC26]\segment_0.19.49.54-0.19.51.29.wav'
[2025-06-05T19:03:06.688Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] Hana wa Saku Shura no Gotoku - 08 [1080p HIDIVE WEB-DL AVC AAC][8596CC26]\segment_0.17.35.33-0.17.37.33.wav: ENOENT: no such file or directory, unlink 'C:\Users\<USER>\Desktop\lycoris.cafe - bot\app\audio_processing\[Erai-raws] Hana wa Saku Shura no Gotoku - 08 [1080p HIDIVE WEB-DL AVC AAC][8596CC26]\segment_0.17.35.33-0.17.37.33.wav'
[2025-06-05T19:03:06.704Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] Hana wa Saku Shura no Gotoku - 08 [1080p HIDIVE WEB-DL AVC AAC][8596CC26]\segment_0.21.31.00-0.21.34.92.wav: ENOENT: no such file or directory, unlink 'C:\Users\<USER>\Desktop\lycoris.cafe - bot\app\audio_processing\[Erai-raws] Hana wa Saku Shura no Gotoku - 08 [1080p HIDIVE WEB-DL AVC AAC][8596CC26]\segment_0.21.31.00-0.21.34.92.wav'
[2025-06-05T19:03:09.393Z] (node:12220) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-06-06T14:08:06.783Z] (node:17184) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-06-06T16:21:38.389Z] (node:10988) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-06-06T18:11:44.445Z] (node:12896) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-06-07T09:58:21.901Z] (node:14736) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-06-07T10:01:38.387Z] [31m[ERROR] Error translating files: YAIBA.Samurai.Legend.S01E10.Onimarus.Devil.Kings.1080p.NF.WEB-DL.JPN.AAC2.0.H.264.MSubs-ToonsHub_eng.txt  Error: [31m[ERROR] Line mismatch encountered during translations. Ending the job after 5 attempts.
[2025-06-07T10:01:38.501Z] [31m[ERROR] Error in translating process: Command failed: node app/2translate/translate.js
[2025-06-07T10:26:46.989Z] (node:12596) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-06-07T12:18:26.321Z] (node:14800) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-06-07T12:21:58.940Z] [31m[ERROR] Error translating files: YAIBA.Samurai.Legend.S01E10.REPACK.Onimarus.Devil.Kings.1080p.NF.WEB-DL.JPN.AAC2.0.H.264.MSubs-ToonsHub_eng.txt  Error: [31m[ERROR] Line mismatch encountered during translations. Ending the job after 5 attempts.
[2025-06-07T12:21:59.010Z] [31m[ERROR] Error in translating process: Command failed: node app/2translate/translate.js
[2025-06-07T13:26:02.493Z] (node:2748) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-06-07T13:32:46.865Z] (node:17236) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-06-07T16:40:02.561Z] (node:1096) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-06-07T17:22:52.732Z] (node:11304) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-06-07T18:06:58.782Z] (node:10712) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-06-07T18:21:38.605Z] (node:8140) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-06-07T19:41:00.705Z] (node:4456) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-06-08T09:57:24.342Z] [31m[ERROR] Failed to create segment for 0:01:01.35-0:01:01.35: Failed to split audio segment: Command failed: ffmpeg -i "app\audio_processing\[Erai-raws] Uma Musume - Cinderella Gray - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][D3217C31]\full.wav" -ss 0:01:01.35 -to 0:01:01.35 -acodec copy "app\audio_processing\[Erai-raws] Uma Musume - Cinderella Gray - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][D3217C31]\segment_0.01.01.35-0.01.01.35.wav" -loglevel warning -stats -y
[aist#0:0/pcm_s16le @ 000002c3f43eb4c0] Guessed Channel Layout: stereo
[out#0 @ 000002c3f43ff840] -to value smaller than -ss; aborting.
Error opening output file app\audio_processing\[Erai-raws] Uma Musume - Cinderella Gray - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][D3217C31]\segment_0.01.01.35-0.01.01.35.wav.
Error opening output files: Invalid argument

[2025-06-08T09:57:24.342Z] [31m[ERROR] Failed to create segment for 0:02:50.22-0:02:50.22: Failed to split audio segment: Command failed: ffmpeg -i "app\audio_processing\[Erai-raws] Uma Musume - Cinderella Gray - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][D3217C31]\full.wav" -ss 0:02:50.22 -to 0:02:50.22 -acodec copy "app\audio_processing\[Erai-raws] Uma Musume - Cinderella Gray - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][D3217C31]\segment_0.02.50.22-0.02.50.22.wav" -loglevel warning -stats -y
[aist#0:0/pcm_s16le @ 000001f2c4ecb4c0] Guessed Channel Layout: stereo
[out#0 @ 000001f2c4edf840] -to value smaller than -ss; aborting.
Error opening output file app\audio_processing\[Erai-raws] Uma Musume - Cinderella Gray - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][D3217C31]\segment_0.02.50.22-0.02.50.22.wav.
Error opening output files: Invalid argument

[2025-06-08T09:57:24.404Z] [31m[ERROR] Failed to create segment for 0:07:43.62-0:07:43.62: Failed to split audio segment: Command failed: ffmpeg -i "app\audio_processing\[Erai-raws] Uma Musume - Cinderella Gray - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][D3217C31]\full.wav" -ss 0:07:43.62 -to 0:07:43.62 -acodec copy "app\audio_processing\[Erai-raws] Uma Musume - Cinderella Gray - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][D3217C31]\segment_0.07.43.62-0.07.43.62.wav" -loglevel warning -stats -y
[aist#0:0/pcm_s16le @ 0000023414d3b4c0] Guessed Channel Layout: stereo
[out#0 @ 0000023414d4f840] -to value smaller than -ss; aborting.
Error opening output file app\audio_processing\[Erai-raws] Uma Musume - Cinderella Gray - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][D3217C31]\segment_0.07.43.62-0.07.43.62.wav.
Error opening output files: Invalid argument

[2025-06-08T09:57:24.436Z] [31m[ERROR] Failed to create segment for 0:10:47.08-0:10:47.08: Failed to split audio segment: Command failed: ffmpeg -i "app\audio_processing\[Erai-raws] Uma Musume - Cinderella Gray - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][D3217C31]\full.wav" -ss 0:10:47.08 -to 0:10:47.08 -acodec copy "app\audio_processing\[Erai-raws] Uma Musume - Cinderella Gray - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][D3217C31]\segment_0.10.47.08-0.10.47.08.wav" -loglevel warning -stats -y
[aist#0:0/pcm_s16le @ 000001e14b26b4c0] Guessed Channel Layout: stereo
[out#0 @ 000001e14b27f840] -to value smaller than -ss; aborting.
Error opening output file app\audio_processing\[Erai-raws] Uma Musume - Cinderella Gray - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][D3217C31]\segment_0.10.47.08-0.10.47.08.wav.
Error opening output files: Invalid argument

[2025-06-08T09:57:31.622Z] [31m[ERROR] Failed to create segment for 0:14:39.92-0:14:39.92: Failed to split audio segment: Command failed: ffmpeg -i "app\audio_processing\[Erai-raws] Uma Musume - Cinderella Gray - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][D3217C31]\full.wav" -ss 0:14:39.92 -to 0:14:39.92 -acodec copy "app\audio_processing\[Erai-raws] Uma Musume - Cinderella Gray - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][D3217C31]\segment_0.14.39.92-0.14.39.92.wav" -loglevel warning -stats -y
[aist#0:0/pcm_s16le @ 00000193d983b4c0] Guessed Channel Layout: stereo
[out#0 @ 00000193d984f840] -to value smaller than -ss; aborting.
Error opening output file app\audio_processing\[Erai-raws] Uma Musume - Cinderella Gray - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][D3217C31]\segment_0.14.39.92-0.14.39.92.wav.
Error opening output files: Invalid argument

[2025-06-08T09:57:51.753Z] [31m[ERROR] Failed to create segment for 0:21:01.28-0:21:01.28: Failed to split audio segment: Command failed: ffmpeg -i "app\audio_processing\[Erai-raws] Uma Musume - Cinderella Gray - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][D3217C31]\full.wav" -ss 0:21:01.28 -to 0:21:01.28 -acodec copy "app\audio_processing\[Erai-raws] Uma Musume - Cinderella Gray - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][D3217C31]\segment_0.21.01.28-0.21.01.28.wav" -loglevel warning -stats -y
[aist#0:0/pcm_s16le @ 000001d4a598b4c0] Guessed Channel Layout: stereo
[out#0 @ 000001d4a599f840] -to value smaller than -ss; aborting.
Error opening output file app\audio_processing\[Erai-raws] Uma Musume - Cinderella Gray - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][D3217C31]\segment_0.21.01.28-0.21.01.28.wav.
Error opening output files: Invalid argument

[2025-06-08T09:58:06.463Z] [31m[ERROR] Failed to create segment for 0:21:02.39-0:21:02.39: Failed to split audio segment: Command failed: ffmpeg -i "app\audio_processing\[Erai-raws] Uma Musume - Cinderella Gray - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][D3217C31]\full.wav" -ss 0:21:02.39 -to 0:21:02.39 -acodec copy "app\audio_processing\[Erai-raws] Uma Musume - Cinderella Gray - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][D3217C31]\segment_0.21.02.39-0.21.02.39.wav" -loglevel warning -stats -y
[aist#0:0/pcm_s16le @ 0000018bef21b4c0] Guessed Channel Layout: stereo
[out#0 @ 0000018bef22f840] -to value smaller than -ss; aborting.
Error opening output file app\audio_processing\[Erai-raws] Uma Musume - Cinderella Gray - 10 [1080p AMZN WEB-DL AVC EAC3][MultiSub][D3217C31]\segment_0.21.02.39-0.21.02.39.wav.
Error opening output files: Invalid argument

[2025-06-08T10:00:36.733Z] (node:10140) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-06-08T14:41:31.150Z] (node:10188) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-06-08T15:31:15.133Z] (node:16908) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-06-08T16:18:51.677Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] aiomodarkelf - 10 [1080p ADN WEB-DL AVC AAC][MultiSub][12F7CDBF]\segment_0.02.08.60-0.02.10.60.wav: ENOENT: no such file or directory, unlink 'C:\Users\<USER>\Desktop\lycoris.cafe - bot\app\audio_processing\[Erai-raws] aiomodarkelf - 10 [1080p ADN WEB-DL AVC AAC][MultiSub][12F7CDBF]\segment_0.02.08.60-0.02.10.60.wav'
[2025-06-08T16:18:51.692Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] aiomodarkelf - 10 [1080p ADN WEB-DL AVC AAC][MultiSub][12F7CDBF]\segment_0.02.08.60-0.02.10.60.wav: ENOENT: no such file or directory, unlink 'C:\Users\<USER>\Desktop\lycoris.cafe - bot\app\audio_processing\[Erai-raws] aiomodarkelf - 10 [1080p ADN WEB-DL AVC AAC][MultiSub][12F7CDBF]\segment_0.02.08.60-0.02.10.60.wav'
[2025-06-08T16:18:51.692Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] aiomodarkelf - 10 [1080p ADN WEB-DL AVC AAC][MultiSub][12F7CDBF]\segment_0.02.10.77-0.02.11.85.wav: ENOENT: no such file or directory, unlink 'C:\Users\<USER>\Desktop\lycoris.cafe - bot\app\audio_processing\[Erai-raws] aiomodarkelf - 10 [1080p ADN WEB-DL AVC AAC][MultiSub][12F7CDBF]\segment_0.02.10.77-0.02.11.85.wav'
[2025-06-08T16:18:51.708Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] aiomodarkelf - 10 [1080p ADN WEB-DL AVC AAC][MultiSub][12F7CDBF]\segment_0.05.31.73-0.05.32.60.wav: ENOENT: no such file or directory, unlink 'C:\Users\<USER>\Desktop\lycoris.cafe - bot\app\audio_processing\[Erai-raws] aiomodarkelf - 10 [1080p ADN WEB-DL AVC AAC][MultiSub][12F7CDBF]\segment_0.05.31.73-0.05.32.60.wav'
[2025-06-08T16:20:32.172Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] aiomodarkelf - 10 [1080p HIDIVE WEB-DL AVC AAC][1923B26D]\segment_0.02.04.50-0.02.06.50.wav: ENOENT: no such file or directory, unlink 'C:\Users\<USER>\Desktop\lycoris.cafe - bot\app\audio_processing\[Erai-raws] aiomodarkelf - 10 [1080p HIDIVE WEB-DL AVC AAC][1923B26D]\segment_0.02.04.50-0.02.06.50.wav'
[2025-06-08T16:20:32.172Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] aiomodarkelf - 10 [1080p HIDIVE WEB-DL AVC AAC][1923B26D]\segment_0.02.06.67-0.02.07.75.wav: ENOENT: no such file or directory, unlink 'C:\Users\<USER>\Desktop\lycoris.cafe - bot\app\audio_processing\[Erai-raws] aiomodarkelf - 10 [1080p HIDIVE WEB-DL AVC AAC][1923B26D]\segment_0.02.06.67-0.02.07.75.wav'
[2025-06-08T16:20:32.172Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] aiomodarkelf - 10 [1080p HIDIVE WEB-DL AVC AAC][1923B26D]\segment_0.02.04.50-0.02.06.50.wav: ENOENT: no such file or directory, unlink 'C:\Users\<USER>\Desktop\lycoris.cafe - bot\app\audio_processing\[Erai-raws] aiomodarkelf - 10 [1080p HIDIVE WEB-DL AVC AAC][1923B26D]\segment_0.02.04.50-0.02.06.50.wav'
[2025-06-08T16:20:32.203Z] [31m[ERROR] Failed to delete segment app\audio_processing\[Erai-raws] aiomodarkelf - 10 [1080p HIDIVE WEB-DL AVC AAC][1923B26D]\segment_0.05.27.63-0.05.28.50.wav: ENOENT: no such file or directory, unlink 'C:\Users\<USER>\Desktop\lycoris.cafe - bot\app\audio_processing\[Erai-raws] aiomodarkelf - 10 [1080p HIDIVE WEB-DL AVC AAC][1923B26D]\segment_0.05.27.63-0.05.28.50.wav'
[2025-06-08T16:20:34.058Z] (node:6852) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-06-08T18:14:55.994Z] (node:4700) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-06-29T10:35:25.989Z] (node:11800) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-06-29T10:48:18.250Z] [31m[ERROR] Failed to create segment for 0:05:43.11-0:05:43.11: Failed to split audio segment: Command failed: ffmpeg -i "app\audio_processing\[Erai-raws] Uma Musume - Cinderella Gray - 13 [1080p AMZN WEB-DL AVC EAC3][MultiSub][713DD8D7]\full.wav" -ss 0:05:43.11 -to 0:05:43.11 -acodec copy "app\audio_processing\[Erai-raws] Uma Musume - Cinderella Gray - 13 [1080p AMZN WEB-DL AVC EAC3][MultiSub][713DD8D7]\segment_0.05.43.11-0.05.43.11.wav" -loglevel warning -stats -y
[aist#0:0/pcm_s16le @ 00000182abaa9500] Guessed Channel Layout: stereo
[out#0 @ 00000182abd3ae80] -to value smaller than -ss; aborting.
Error opening output file app\audio_processing\[Erai-raws] Uma Musume - Cinderella Gray - 13 [1080p AMZN WEB-DL AVC EAC3][MultiSub][713DD8D7]\segment_0.05.43.11-0.05.43.11.wav.
Error opening output files: Invalid argument

[2025-06-29T10:50:42.783Z] (node:15304) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-06-29T13:10:24.145Z] (node:11276) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-06-29T13:13:46.778Z] [31m[ERROR] Error translating files: [Erai-raws] Uma Musume - Cinderella Gray - 12 [1080p AMZN WEB-DL AVC EAC3][MultiSub][25823FC5]_eng.txt  Error: [31m[ERROR] Line mismatch encountered during translations. Ending the job after 5 attempts.
[2025-06-29T13:13:46.821Z] [31m[ERROR] Error in translating process: Command failed: node app/2translate/translate.js
[2025-06-30T15:11:01.049Z] (node:12868) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-06-30T15:27:10.534Z] (node:15368) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-07-01T13:08:41.851Z] (node:13484) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
[2025-07-02T13:10:00.768Z] [ERROR] Attempt 1: Failed to fetch RSS feed: read ECONNRESET
